import pandas as pd

try:
    import spotfire
    platform = 'spotfire'
except ImportError:
    platform = 'ide'

if platform == 'ide':
    recipe_table = pd.read_excel("recipe_table.xlsx", sheet_name='Sheet1')

# 截取PHT和ITO/UPK
recipe_table_pht = recipe_table['PHT' in recipe_table['MACHINE NAME']]
recipe_table_others = recipe_table['PHT' not in recipe_table['MACHINE NAME']]

# PHT处理
# MACHINE NAME列是设备名、PRODCUT ID是产品名、UNITRECIPENAME是0001~0999的数字、LAYER是制程名
# 我希望最终处理成表头是:Recipe No.、是否占用、{设备1}、{设备2}...
# Recipe No.列即为UNITRECIPENAME，源是长度位4的字符串，我希望处理成001~999的整型
# 当某个No.的任意一个设备有内容时，是否占用为"是"，否则为空
# 设备下的单元格内容是'{PRODUCT ID_1}({LAYER_1});{PRODUCT ID_2}({LAYER_2})...'的格式
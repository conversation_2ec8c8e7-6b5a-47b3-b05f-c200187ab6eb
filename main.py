import pandas as pd

try:
    import spotfire
    platform = 'spotfire'
except ImportError:
    platform = 'ide'

if platform == 'ide':
    recipe_table = pd.read_excel("recipe_table.xlsx", sheet_name='Sheet1')

# 处理空的LAYER值，将其设置为'ukn'
recipe_table['LAYER'] = recipe_table['LAYER'].fillna('ukn')
recipe_table['LAYER'] = recipe_table['LAYER'].replace('', 'ukn')

# 截取PHT和ITO/UPK
recipe_table_pht = recipe_table[recipe_table['MACHINE NAME'].str.contains('PHT', na=False)]
recipe_table_others = recipe_table[~recipe_table['MACHINE NAME'].str.contains('PHT', na=False)]

# PHT处理
# MACHINE NAME列是设备名、PRODCUT ID是产品名、UNITRECIPENAME是0001~0999的数字、LAYER是制程名
# 我希望最终处理成表头是:Recipe No.、是否占用、{设备1}、{设备2}...
# Recipe No.列即为UNITRECIPENAME，源是长度位4的字符串，我希望处理成001~999（全序列）的整型
# 当某个No.的任意一个设备有内容时，是否占用为"是"，否则为空
# 设备下的单元格内容是'{PRODUCT ID_1}({LAYER_1});{PRODUCT ID_2}({LAYER_2})...'的格式
# 特殊处理：当有一个或多个PRODUCT ID的LAYER为DMY时，聚合成一个DMY即可。例如 A(DMY);B(DMY);C(R)聚合成DMY;C(R)

# 获取所有PHT设备名
pht_machines = sorted(recipe_table_pht['MACHINE NAME'].unique())

# 创建完整的Recipe No.序列 (1-999)
recipe_nos = list(range(1, 1000))

# 创建结果DataFrame
pht_result = pd.DataFrame()
pht_result['Recipe No.'] = recipe_nos
pht_result['是否占用'] = ''

# 为每个设备创建列
for machine in pht_machines:
    pht_result[machine] = ''

# 处理每个Recipe No.
for recipe_no in recipe_nos:
    # 将Recipe No.转换为4位字符串格式进行匹配
    recipe_str = str(recipe_no).zfill(4)

    # 获取该Recipe No.的所有记录
    recipe_data = recipe_table_pht[recipe_table_pht['UNITRECIPENAME'].astype(str).str.zfill(4) == recipe_str]

    if len(recipe_data) > 0:
        pht_result.loc[pht_result['Recipe No.'] == recipe_no, '是否占用'] = '是'

        # 按设备分组处理
        for machine in pht_machines:
            machine_data = recipe_data[recipe_data['MACHINE NAME'] == machine]

            if len(machine_data) > 0:
                # 构建内容字符串
                content_parts = []
                dmy_products = []
                other_parts = []

                for _, row in machine_data.iterrows():
                    product_id = row['PRODUCT ID']
                    layer = row['LAYER']

                    # 如果LAYER是空，则设置为ukn
                    if pd.isna(layer) or layer == '' or layer is None:
                        layer = 'ukn'

                    if layer == 'DMY':
                        dmy_products.append(product_id)
                    else:
                        other_parts.append("{}({})".format(product_id, layer))

                # 处理DMY：如果有DMY，只保留一个DMY
                if dmy_products:
                    content_parts.append('DMY')

                # 添加其他部分
                content_parts.extend(other_parts)

                # 组合最终内容
                final_content = ';'.join(content_parts)
                pht_result.loc[pht_result['Recipe No.'] == recipe_no, machine] = final_content
    else:
        pht_result.loc[pht_result['Recipe No.'] == recipe_no, '是否占用'] = '否'

# ITO/UPK处理
# 处理成PRODUCT ID、ITO、UPK。ITO和UPK列填Recipe No.，缺失则留空

# 获取所有产品ID
product_ids = sorted(recipe_table_others['PRODUCT ID'].unique())

# 创建结果DataFrame
ito_upk_result = pd.DataFrame()
ito_upk_result['PRODUCT ID'] = product_ids
ito_upk_result['ITO'] = ''
ito_upk_result['UPK'] = ''

# 处理每个产品ID
for product_id in product_ids:
    product_data = recipe_table_others[recipe_table_others['PRODUCT ID'] == product_id]

    # 处理ITO
    ito_data = product_data[product_data['MACHINE NAME'].str.contains('ITO', na=False)]
    if len(ito_data) > 0:
        # 获取Recipe No.，转换为整数
        recipe_no = int(ito_data.iloc[0]['UNITRECIPENAME'])
        ito_upk_result.loc[ito_upk_result['PRODUCT ID'] == product_id, 'ITO'] = recipe_no

    # 处理UPK
    upk_data = product_data[product_data['MACHINE NAME'].str.contains('UPK', na=False)]
    if len(upk_data) > 0:
        # 获取Recipe No.，转换为整数
        recipe_no = int(upk_data.iloc[0]['UNITRECIPENAME'])
        ito_upk_result.loc[ito_upk_result['PRODUCT ID'] == product_id, 'UPK'] = recipe_no

# 保存结果到Excel文件
import datetime
timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
filename = 'processed_recipe_table_{}.xlsx'.format(timestamp)

with pd.ExcelWriter(filename, engine='openpyxl') as writer:
    pht_result.to_excel(writer, sheet_name='PHT', index=False)
    ito_upk_result.to_excel(writer, sheet_name='ITO_UPK', index=False)

print("处理完成！结果已保存到 {}".format(filename))

